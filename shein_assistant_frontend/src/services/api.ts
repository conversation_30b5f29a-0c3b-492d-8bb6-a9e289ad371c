import axios from "axios";
import { Client, ProcessedClient, EmailResult, OrderHistory } from "../types";

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000, // 2 minutes timeout for scraping
});

export const apiService = {
  // Generate order summaries by scraping Shein carts
  async generateOrders(requestData: {
    clients: Client[];
    exchange_rate: number;
    orderDate: string;
    currency: {
      from: string;
      to: string;
    };
  }): Promise<{
    success: boolean;
    message: string;
    results: ProcessedClient[];
  }> {
    const response = await api.post("/api/generate", requestData);
    return response.data;
  },

  // Send emails to selected clients
  async sendEmails(selectedClients: ProcessedClient["data"][]): Promise<{
    success: boolean;
    message: string;
    results: EmailResult[];
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }> {
    const response = await api.post("/api/send", { selectedClients });
    return response.data;
  },

  // Get order history
  async getOrderHistory(): Promise<{
    success: boolean;
    orders: OrderHistory[];
  }> {
    const response = await api.get("/api/history");
    return response.data;
  },

  // Get specific order by ID
  async getOrder(id: string): Promise<{
    success: boolean;
    order: OrderHistory;
  }> {
    const response = await api.get(`/api/history/${id}`);
    return response.data;
  },

  // Delete order by ID
  async deleteOrder(id: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await api.delete(`/api/history/${id}`);
    return response.data;
  },

  // Health check
  async healthCheck(): Promise<{
    status: string;
    message: string;
  }> {
    const response = await api.get("/api/health");
    return response.data;
  },

  // Authentication
  async validateAuth(code: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await api.post("/api/auth/validate", { code });
    return response.data;
  },
};

export default apiService;
