import React, { useState } from "react";
import { Lock, ShoppingCart, Eye, EyeOff } from "lucide-react";
import { authenticate } from "../utils/auth";

interface SecurityPageProps {
  onAuthenticated: () => void;
}

const SecurityPage: React.FC<SecurityPageProps> = ({ onAuthenticated }) => {
  const [code, setCode] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showCode, setShowCode] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    // Add a small delay to prevent brute force attempts
    await new Promise((resolve) => setTimeout(resolve, 500));

    if (code.length !== 4) {
      setError("Le code doit contenir exactement 4 caractères");
      setIsLoading(false);
      return;
    }

    const isValid = await authenticate(code);

    if (isValid) {
      onAuthenticated();
    } else {
      setError("Code incorrect. Veuillez réessayer.");
      setCode("");
    }

    setIsLoading(false);
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.slice(0, 4); // Limit to 4 characters
    setCode(value);
    if (error) setError(""); // Clear error when user starts typing
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-white flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Logo/Header */}
        <div className="text-center mb-8">
          <div className="bg-gradient-to-r from-purple-500 to-blue-600 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg">
            <ShoppingCart className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
            Assistant Commandes Shein
          </h1>
          <p className="text-gray-600 font-medium">Accès sécurisé requis</p>
        </div>

        {/* Security Form */}
        <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-white/20 p-8">
          <div className="text-center mb-6">
            <div className="bg-gradient-to-r from-purple-100 to-blue-100 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Lock className="h-8 w-8 text-purple-600" />
            </div>
            <h2 className="text-xl font-semibold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
              Code d'accès
            </h2>
            <p className="text-gray-600 text-sm font-medium">
              Veuillez entrer le code à 4 caractères pour accéder à
              l'application
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="security-code" className="sr-only">
                Code de sécurité
              </label>
              <div className="relative">
                <input
                  id="security-code"
                  type={showCode ? "text" : "password"}
                  value={code}
                  onChange={handleCodeChange}
                  placeholder="••••"
                  className={`w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                    error
                      ? "border-red-300 bg-red-50"
                      : "border-purple-200 bg-white/80 text-gray-700 shadow-sm hover:shadow-md"
                  }`}
                  maxLength={4}
                  autoComplete="off"
                  autoFocus
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowCode(!showCode)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-600 transition-colors duration-200"
                  disabled={isLoading}
                >
                  {showCode ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {error && (
                <p className="mt-2 text-sm text-red-600 text-center">{error}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={code.length !== 4 || isLoading}
              className={`w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 shadow-lg ${
                code.length === 4 && !isLoading
                  ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-purple-200 hover:shadow-xl"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed shadow-gray-200"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Vérification...
                </div>
              ) : (
                "Accéder"
              )}
            </button>
          </form>

          {/* Security notice */}
          <div className="mt-6 p-3 bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-xl">
            <p className="text-xs text-purple-800 text-center font-medium">
              🔒 Code de sécurité requis pour accéder à l'application
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500 font-medium">
            Gérez facilement les commandes de vos clients
          </p>
        </div>
      </div>
    </div>
  );
};

export default SecurityPage;
