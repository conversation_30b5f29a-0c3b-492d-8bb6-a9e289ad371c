import {
  CheckCircle,
  XCircle,
  Package,
  DollarSign,
  ChevronDown,
} from "lucide-react";
import { ProcessedClient } from "../types";
import { calculateClientTotals } from "../utils/calculations";

interface ResultsDisplayProps {
  results: ProcessedClient[];
  setResults: (results: ProcessedClient[]) => void;
}

const ResultsDisplay = ({ results, setResults }: ResultsDisplayProps) => {
  const successfulResults = results.filter((r) => r.success && r.data);
  const failedResults = results.filter((r) => !r.success);

  // Calculate totals dynamically from current product quantities
  const total_usd = successfulResults.reduce((sum, r) => {
    if (!r.data?.products || !r.data?.exchangeRate) return sum;
    const clientTotals = calculateClientTotals(
      r.data.products,
      r.data.exchangeRate
    );
    return sum + clientTotals.totalUsd;
  }, 0);

  const total_mur = successfulResults.reduce((sum, r) => {
    if (!r.data?.products || !r.data?.exchangeRate) return sum;
    const clientTotals = calculateClientTotals(
      r.data.products,
      r.data.exchangeRate
    );
    return sum + clientTotals.totalMur;
  }, 0);

  return (
    <div>
      <div className="flex items-center space-x-4 mb-8">
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl shadow-lg">
          <Package className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            Résultats de l'Analyse
          </h2>
          <p className="text-sm text-gray-500 font-medium">
            Analyse des paniers et calcul des totaux
          </p>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gradient-to-r from-blue-50/80 to-blue-100/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-blue-200/30">
          <div className="flex items-center space-x-3 mb-2">
            <CheckCircle className="h-6 w-6 text-blue-600" />
            <span className="text-sm font-semibold text-blue-700">Succès</span>
          </div>
          <p className="text-3xl font-bold text-blue-900">
            {successfulResults.length}
          </p>
        </div>

        <div className="bg-gradient-to-r from-red-50/80 to-red-100/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-red-200/30">
          <div className="flex items-center space-x-3 mb-2">
            <XCircle className="h-6 w-6 text-red-600" />
            <span className="text-sm font-semibold text-red-700">Échecs</span>
          </div>
          <p className="text-3xl font-bold text-red-900">
            {failedResults.length}
          </p>
        </div>

        <div className="bg-gradient-to-r from-purple-50/80 via-blue-50/80 to-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-purple-200/30">
          <div className="flex items-center space-x-3 mb-2">
            <DollarSign className="h-6 w-6 text-green-600" />
            <span className="text-sm font-semibold text-green-700">
              Total USD
            </span>
          </div>
          <p className="text-3xl font-bold text-green-900">
            ${total_usd.toFixed(2)}
          </p>
        </div>

        <div className="bg-gradient-to-r from-purple-50/80 to-purple-100/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-purple-200/30">
          <div className="flex items-center space-x-3 mb-2">
            <DollarSign className="h-6 w-6 text-purple-600" />
            <span className="text-sm font-semibold text-purple-700">
              Total MUR
            </span>
          </div>
          <p className="text-3xl font-bold text-purple-900">
            Rs {total_mur.toLocaleString()}
          </p>
        </div>
      </div>

      {/* Successful Results */}
      {successfulResults.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-bold text-gray-800 mb-6 flex items-center space-x-2">
            <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"></div>
            <span>Commandes Traitées avec Succès</span>
          </h3>
          <div className="space-y-6">
            {successfulResults.map((result, index) => {
              // Calculate dynamic totals for this client
              const clientTotals =
                result.data?.products && result.data?.exchangeRate
                  ? calculateClientTotals(
                      result.data.products,
                      result.data.exchangeRate
                    )
                  : { totalUsd: 0, totalMur: 0 };

              return (
                <div
                  key={index}
                  className="bg-gradient-to-r from-purple-50/80 via-blue-50/80 to-white/80 backdrop-blur-sm border border-purple-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl shadow-lg">
                        <CheckCircle className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-bold text-gray-800 text-lg">
                          {result.data?.name}
                        </h4>
                        <p className="text-sm text-gray-600 font-medium">
                          {result.data?.email}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-purple-700">
                        ${clientTotals.totalUsd.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-600 font-semibold">
                        Rs {clientTotals.totalMur.toLocaleString()}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-white/60 backdrop-blur-sm p-3 rounded-lg">
                      <span className="text-gray-600 text-xs font-semibold uppercase tracking-wide">
                        Taux:
                      </span>
                      <p className="font-bold text-gray-800 text-lg">
                        {result.data?.exchangeRate}
                      </p>
                    </div>
                    <div className="bg-white/60 backdrop-blur-sm p-3 rounded-lg">
                      <span className="text-gray-600 text-xs font-semibold uppercase tracking-wide">
                        Paniers:
                      </span>
                      <p className="font-bold text-gray-800 text-lg">
                        {result.data?.cartCount}
                      </p>
                    </div>
                    <div className="bg-white/60 backdrop-blur-sm p-3 rounded-lg">
                      <span className="text-gray-600 text-xs font-semibold uppercase tracking-wide">
                        Produits:
                      </span>
                      <p className="font-bold text-gray-800 text-lg">
                        {result.data?.productCount}
                      </p>
                    </div>
                    <div className="bg-white/60 backdrop-blur-sm p-3 rounded-lg flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                      <span className="text-green-700 font-bold">Traité</span>
                    </div>
                  </div>

                  {/* Product Details */}
                  {result.data?.products && result.data.products.length > 0 && (
                    <div className="mt-6 pt-6 border-t border-purple-200/50">
                      <details className="group">
                        <summary className="cursor-pointer text-sm font-bold text-purple-700 hover:text-purple-800 flex items-center space-x-2 p-3 bg-white/40 rounded-lg transition-all duration-200 hover:bg-white/60">
                          <ChevronDown className="h-4 w-4 transition-transform group-open:rotate-180" />
                          <span>
                            Voir les produits ({result.data.products.length})
                          </span>
                        </summary>
                        <div className="mt-4 space-y-4">
                          {result.data.products.map((product, productIndex) => {
                            // Extract numeric price
                            const unitPrice =
                              parseFloat(
                                product.price.replace(/[^\d.-]/g, "")
                              ) || 0;
                            const quantity = product.quantity ?? 1;
                            const totalPrice = unitPrice * quantity;

                            const updateQuantity = (delta: number) => {
                              const newQuantity = Math.max(1, quantity + delta);
                              results[index].data!.products[productIndex] = {
                                ...product,
                                quantity: newQuantity,
                              };
                              setResults([...results]); // Trigger re-render
                            };

                            return (
                              <div
                                key={productIndex}
                                className="bg-white/80 backdrop-blur-sm p-5 rounded-xl border border-gray-200/50 shadow-md hover:shadow-lg transition-all duration-200"
                              >
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  {/* Product name */}
                                  <div className="col-span-2">
                                    <p className="font-bold text-gray-800 mb-3 text-base">
                                      {product.name}
                                    </p>
                                    <div className="space-y-2">
                                      <div className="flex items-center space-x-2">
                                        <span className="text-gray-600 text-sm font-medium">
                                          Prix unitaire:
                                        </span>
                                        <span className="font-bold text-gray-800 bg-gray-100 px-2 py-1 rounded-md">
                                          €{unitPrice.toFixed(2)}
                                        </span>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <span className="text-gray-600 text-sm font-medium">
                                          Prix total:
                                        </span>
                                        <span className="font-bold text-gray-800 bg-gray-100 px-2 py-1 rounded-md">
                                          €{totalPrice.toFixed(2)}
                                        </span>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Quantity & Controls */}
                                  <div className="flex flex-col md:items-end space-y-3">
                                    <div className="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-xl border border-blue-200/50">
                                      <button
                                        onClick={() => updateQuantity(-1)}
                                        className="w-8 h-8 flex items-center justify-center text-sm rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold shadow-md hover:shadow-lg transition-all duration-200"
                                      >
                                        -
                                      </button>
                                      <div className="bg-white px-4 py-2 rounded-lg border border-blue-200 shadow-sm">
                                        <span className="text-blue-700 font-bold text-sm">
                                          Qté: {quantity}
                                        </span>
                                      </div>
                                      <button
                                        onClick={() => updateQuantity(+1)}
                                        className="w-8 h-8 flex items-center justify-center text-sm rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold shadow-md hover:shadow-lg transition-all duration-200"
                                      >
                                        +
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </details>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Failed Results */}
      {failedResults.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-bold text-gray-800 mb-6 flex items-center space-x-2">
            <div className="w-2 h-2 bg-gradient-to-r from-red-500 to-rose-600 rounded-full"></div>
            <span>Erreurs de Traitement</span>
          </h3>
          <div className="space-y-4">
            {failedResults.map((result, index) => (
              <div
                key={index}
                className="bg-gradient-to-r from-red-50/80 to-rose-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <div className="flex items-start space-x-4">
                  <div className="bg-gradient-to-r from-red-500 to-rose-600 p-3 rounded-xl shadow-lg">
                    <XCircle className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-gray-800 text-lg mb-2">
                      {result.client}
                    </h4>
                    <div className="bg-white/60 backdrop-blur-sm p-4 rounded-lg border border-red-200/50">
                      <p className="text-sm text-red-700 font-medium leading-relaxed">
                        {result.error}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {results.length === 0 && (
        <div className="text-center py-12">
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 backdrop-blur-sm border border-gray-200/50 rounded-xl p-8 shadow-lg">
            <Package className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600 font-medium text-lg">
              Aucun résultat à afficher
            </p>
            <p className="text-gray-500 text-sm mt-2">
              Commencez par traiter des commandes pour voir les résultats ici
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsDisplay;
