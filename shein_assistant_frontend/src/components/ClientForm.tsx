import React, { useState } from "react";
import {
  Plus,
  Minus,
  Loader2,
  <PERSON>,
  Link as LinkIcon,
  ShoppingCart,
} from "lucide-react";
import { Client, ProcessedClient } from "../types";
import apiService from "../services/api";

interface ClientFormProps {
  clients: Client[];
  setClients: (clients: Client[]) => void;
  onResultsUpdate: (results: ProcessedClient[]) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const ClientForm: React.FC<ClientFormProps> = ({
  clients,
  setClients,
  onResultsUpdate,
  isLoading,
  setIsLoading,
}) => {
  const [error, setError] = useState<string>("");
  const [exchange_rate, setexchange_rate] = useState<number>(40); // USD to MUR default rate
  const [orderDate, setOrderDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [fromCurrency, setFromCurrency] = useState<string>("USD");
  const [toCurrency, setToCurrency] = useState<string>("MUR");

  const addClient = () => {
    const newClient: Client = {
      id: Date.now().toString(),
      name: "",
      email: "",
      cartLinks: [""],
    };
    setClients([...clients, newClient]);
  };

  const removeClient = (id: string) => {
    setClients(clients.filter((client) => client.id !== id));
  };

  const updateClient = (id: string, field: keyof Client, value: any) => {
    setClients(
      clients.map((client) =>
        client.id === id ? { ...client, [field]: value } : client
      )
    );
  };

  const addCartLink = (clientId: string) => {
    setClients(
      clients.map((client) =>
        client.id === clientId
          ? { ...client, cartLinks: [...client.cartLinks, ""] }
          : client
      )
    );
  };

  const removeCartLink = (clientId: string, linkIndex: number) => {
    setClients(
      clients.map((client) =>
        client.id === clientId
          ? {
              ...client,
              cartLinks: client.cartLinks.filter((_, i) => i !== linkIndex),
            }
          : client
      )
    );
  };

  const updateCartLink = (
    clientId: string,
    linkIndex: number,
    value: string
  ) => {
    setClients(
      clients.map((client) =>
        client.id === clientId
          ? {
              ...client,
              cartLinks: client.cartLinks.map((link, i) =>
                i === linkIndex ? value : link
              ),
            }
          : client
      )
    );
  };

  const validateForm = (): string | null => {
    if (clients.length === 0) {
      return "Veuillez ajouter au moins un client";
    }

    if (!exchange_rate || exchange_rate <= 0) {
      return "Le taux de change doit être supérieur à 0";
    }

    if (!orderDate) {
      return "La date de commande est requise";
    }

    for (const client of clients) {
      if (!client.name.trim()) {
        return "Tous les clients doivent avoir un nom";
      }
      if (!client.email.trim() || !client.email.includes("@")) {
        return "Tous les clients doivent avoir un email valide";
      }
      if (client.cartLinks.filter((link) => link.trim()).length === 0) {
        return `${client.name} doit avoir au moins un lien de panier`;
      }
    }

    return null;
  };

  const handleGenerate = async () => {
    setError("");

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);

    try {
      const requestData = {
        clients,
        exchange_rate,
        orderDate,
        currency: {
          from: fromCurrency,
          to: toCurrency,
        },
      };

      const response = await apiService.generateOrders(requestData);

      if (response.success) {
        onResultsUpdate(response.results);
        setError("");
      } else {
        setError("Erreur lors de la génération des commandes");
      }
    } catch (error: any) {
      console.error("Generate error:", error);
      setError(
        error.response?.data?.message || "Erreur de connexion au serveur"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl shadow-lg">
            <Users className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
              Informations Clients
            </h2>
            <p className="text-sm text-gray-500 font-medium">
              Ajoutez les clients et leurs liens de panier
            </p>
          </div>
        </div>
        <button
          onClick={addClient}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
        >
          <Plus className="h-4 w-4" />
          <span className="font-medium">Ajouter Client</span>
        </button>
      </div>

      {error && (
        <div className="mb-8 p-4 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl shadow-lg">
          <p className="text-red-700 text-sm font-medium">{error}</p>
        </div>
      )}

      {/* Global Settings */}
      <div className="mb-8 p-6 bg-gradient-to-r from-blue-50/80 to-purple-50/80 backdrop-blur-sm border border-blue-200/30 rounded-xl shadow-lg">
        <h3 className="text-lg font-bold text-gray-800 mb-6 flex items-center space-x-2">
          <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
          <span>Paramètres de Commande</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Date de Commande *
            </label>
            <input
              type="date"
              value={orderDate}
              onChange={(e) => setOrderDate(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm transition-all duration-200 hover:shadow-md text-gray-800 font-medium"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Devise Source
            </label>
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm transition-all duration-200 hover:shadow-md text-gray-800 font-medium"
            >
              <option value="USD">USD ($)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Devise Cible
            </label>
            <select
              value={toCurrency}
              onChange={(e) => setToCurrency(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm transition-all duration-200 hover:shadow-md text-gray-800 font-medium"
            >
              <option value="MUR">MUR (Rs)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Taux de Change ({fromCurrency}/{toCurrency}) *
            </label>
            <input
              type="number"
              step="0.01"
              value={exchange_rate}
              onChange={(e) =>
                setexchange_rate(parseFloat(e.target.value) || 0)
              }
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm transition-all duration-200 hover:shadow-md text-gray-800 font-medium"
              placeholder="40.00"
              min="0.01"
            />
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {clients.map((client, clientIndex) => (
          <div
            key={client.id}
            className="bg-white/50 backdrop-blur-sm border border-gray-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-gray-800 flex items-center space-x-2">
                <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                <span>Client {clientIndex + 1}</span>
              </h3>
              {clients.length > 1 && (
                <button
                  onClick={() => removeClient(client.id)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-all duration-200"
                >
                  <Minus className="h-5 w-5" />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Nom *
                </label>
                <input
                  type="text"
                  value={client.name}
                  onChange={(e) =>
                    updateClient(client.id, "name", e.target.value)
                  }
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm transition-all duration-200 hover:shadow-md text-gray-800 font-medium"
                  placeholder="Nom du client"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  value={client.email}
                  onChange={(e) =>
                    updateClient(client.id, "email", e.target.value)
                  }
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm transition-all duration-200 hover:shadow-md text-gray-800 font-medium"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-semibold text-gray-700">
                  Liens Paniers Shein *
                </label>
                <button
                  onClick={() => addCartLink(client.id)}
                  className="flex items-center space-x-2 px-3 py-2 text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-lg text-sm font-medium transition-all duration-200"
                >
                  <Plus className="h-4 w-4" />
                  <span>Ajouter lien</span>
                </button>
              </div>

              <div className="space-y-3">
                {client.cartLinks.map((link, linkIndex) => (
                  <div
                    key={linkIndex}
                    className="flex items-center space-x-3 p-3 bg-gray-50/50 rounded-xl"
                  >
                    <LinkIcon className="h-5 w-5 text-blue-500 flex-shrink-0" />
                    <input
                      type="url"
                      value={link}
                      onChange={(e) =>
                        updateCartLink(client.id, linkIndex, e.target.value)
                      }
                      className="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm transition-all duration-200 hover:shadow-md text-gray-800 font-medium"
                      placeholder="https://fr.shein.com/cart/..."
                    />
                    {client.cartLinks.length > 1 && (
                      <button
                        onClick={() => removeCartLink(client.id, linkIndex)}
                        className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-all duration-200"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}

        {clients.length === 0 && (
          <div className="text-center py-16 text-gray-500">
            <div className="bg-gradient-to-r from-blue-100 to-purple-100 p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
              <Users className="h-12 w-12 text-blue-500" />
            </div>
            <p className="text-xl font-semibold mb-2 text-gray-700">
              Aucun client ajouté
            </p>
            <p className="text-sm text-gray-500">
              Cliquez sur "Ajouter Client" pour commencer
            </p>
          </div>
        )}
      </div>

      {clients.length > 0 && (
        <div className="mt-8 pt-6 border-t border-gray-200/50">
          <button
            onClick={handleGenerate}
            disabled={isLoading}
            className="w-full flex items-center justify-center space-x-3 px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-semibold text-lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Génération en cours...</span>
              </>
            ) : (
              <>
                <ShoppingCart className="h-6 w-6" />
                <span>Générer les Totaux</span>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default ClientForm;
