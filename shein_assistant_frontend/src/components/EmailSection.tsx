import React, { useState } from "react";
import { <PERSON>, Send, Loader2, Check<PERSON>ircle, XCircle } from "lucide-react";
import { ProcessedClient, EmailResult } from "../types";
import apiService from "../services/api";

interface EmailSectionProps {
  results: ProcessedClient[];
}

const EmailSection: React.FC<EmailSectionProps> = ({ results }) => {
  const [selectedClients, setSelectedClients] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [emailResults, setEmailResults] = useState<EmailResult[]>([]);
  const [error, setError] = useState<string>("");

  const successfulResults = results.filter((r) => r.success && r.data);

  const toggleClientSelection = (clientName: string) => {
    setSelectedClients((prev) =>
      prev.includes(clientName)
        ? prev.filter((name) => name !== clientName)
        : [...prev, clientName]
    );
  };

  const selectAll = () => {
    setSelectedClients(successfulResults.map((r) => r.data!.name));
  };

  const deselectAll = () => {
    setSelectedClients([]);
  };

  const handleSendEmails = async () => {
    if (selectedClients.length === 0) {
      setError("Veuillez sélectionner au moins un client");
      return;
    }

    setIsLoading(true);
    setError("");
    setEmailResults([]);

    try {
      const clientsToSend = successfulResults
        .filter((r) => selectedClients.includes(r.data!.name))
        .map((r) => r.data!);

      const response = await apiService.sendEmails(clientsToSend);

      if (response.success) {
        setEmailResults(response.results);
        setError("");
      } else {
        setError("Erreur lors de l'envoi des emails");
      }
    } catch (error: any) {
      console.error("Email error:", error);
      setError(
        error.response?.data?.message || "Erreur de connexion au serveur"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="flex items-center space-x-4 mb-8">
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl shadow-lg">
          <Mail className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            Envoi des Emails
          </h2>
          <p className="text-sm text-gray-500 font-medium">
            Envoyez les détails de commande à vos clients
          </p>
        </div>
      </div>

      {error && (
        <div className="mb-8 p-4 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl shadow-lg">
          <p className="text-red-700 text-sm font-medium">{error}</p>
        </div>
      )}

      {successfulResults.length === 0 ? (
        <div className="text-center py-16 text-gray-500">
          <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
            <Mail className="h-12 w-12 text-blue-500" />
          </div>
          <p className="text-xl font-semibold mb-2 text-gray-700">
            Aucun client disponible pour l'envoi d'emails
          </p>
          <p className="text-sm text-gray-500">
            Générez d'abord les totaux avec succès
          </p>
        </div>
      ) : (
        <>
          {/* Client Selection */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-gray-800 flex items-center space-x-2">
                <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"></div>
                <span>Sélectionner les Clients</span>
              </h3>
              <div className="flex items-center space-x-4">
                <button
                  onClick={selectAll}
                  className="text-sm font-medium text-blue-600 hover:text-blue-800 px-3 py-2 rounded-lg hover:bg-blue-50 transition-all duration-200"
                >
                  Tout sélectionner
                </button>
                <div className="w-px h-4 bg-gray-300"></div>
                <button
                  onClick={deselectAll}
                  className="text-sm font-medium text-gray-600 hover:text-gray-800 px-3 py-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
                >
                  Tout désélectionner
                </button>
              </div>
            </div>

            <div className="space-y-3">
              {successfulResults.map((result, index) => {
                return result && result.data ? (
                  <div
                    key={index}
                    className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"
                  >
                    <input
                      type="checkbox"
                      id={`client-${index}`}
                      checked={selectedClients.includes(result.data!.name)}
                      onChange={() => toggleClientSelection(result.data!.name)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label
                      htmlFor={`client-${index}`}
                      className="flex-1 cursor-pointer"
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium text-gray-900">
                            {result?.data?.name}
                          </p>
                          <p className="text-sm text-gray-600">
                            {result?.data?.email}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">
                            Rs {result?.data?.totalMur?.toFixed(2) || "0.00"}
                          </p>
                        </div>
                      </div>
                    </label>
                  </div>
                ) : (
                  <></>
                );
              })}
            </div>

            <div className="mt-4 text-sm text-gray-600">
              {selectedClients.length} client(s) sélectionné(s) sur{" "}
              {successfulResults.length}
            </div>
          </div>

          {/* Send Button */}
          <div className="mb-6">
            <button
              onClick={handleSendEmails}
              disabled={isLoading || selectedClients.length === 0}
              className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Envoi en cours...</span>
                </>
              ) : (
                <>
                  <Send className="h-5 w-5" />
                  <span>Envoyer les Emails ({selectedClients.length})</span>
                </>
              )}
            </button>
          </div>

          {/* Email Results */}
          {emailResults.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Résultats de l'Envoi
              </h3>

              <div className="space-y-3">
                {emailResults.map((result, index) => (
                  <div
                    key={index}
                    className={`flex items-center space-x-3 p-3 rounded-lg border ${
                      result.success
                        ? "bg-green-50 border-green-200"
                        : "bg-red-50 border-red-200"
                    }`}
                  >
                    {result.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">
                        {result.client}
                      </p>
                      <p className="text-sm text-gray-600">{result.email}</p>
                      {result.success ? (
                        <p className="text-sm text-green-600">
                          Email envoyé avec succès
                        </p>
                      ) : (
                        <p className="text-sm text-red-600">{result.error}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {emailResults.length}
                    </p>
                    <p className="text-sm text-gray-600">Total</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {emailResults.filter((r) => r.success).length}
                    </p>
                    <p className="text-sm text-gray-600">Envoyés</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-red-600">
                      {emailResults.filter((r) => !r.success).length}
                    </p>
                    <p className="text-sm text-gray-600">Échecs</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default EmailSection;
