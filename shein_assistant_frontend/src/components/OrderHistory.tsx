import { useState, useEffect } from "react";
import {
  History,
  Calendar,
  Users,
  DollarSign,
  Trash2,
  Eye,
  Loader2,
  Check,
  X,
} from "lucide-react";
import { OrderHistoryRow } from "../types"; // Using the updated interface
import apiService from "../services/api";

interface GroupedOrder {
  id: string;
  timestamp: string;
  clients: {
    name: string;
    email: string;
    totalUsd: number;
    totalMur: number;
    exchangeRate: number;
    productCount: number;
  }[];
  emailResults: {
    client: string;
    email: string;
    success: boolean;
    messageId?: string;
  }[];
}

const OrderHistory = () => {
  const [orders, setOrders] = useState<GroupedOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [selectedOrder, setSelectedOrder] = useState<GroupedOrder | null>(null);

  useEffect(() => {
    loadOrderHistory();
  }, []);

  const groupOrdersById = (orders: OrderHistoryRow[]): GroupedOrder[] => {
    const grouped: Record<string, GroupedOrder> = {};

    orders.forEach((order) => {
      if (!grouped[order.id]) {
        grouped[order.id] = {
          id: order.id,
          timestamp: order.timestamp,
          clients: [],
          emailResults: [],
        };
      }

      grouped[order.id].clients.push({
        name: order.name,
        email: order.client_email, // Now matches the interface
        totalUsd: order.total_usd,
        totalMur: order.total_mur,
        exchangeRate: order.exchange_rate,
        productCount: order.product_count,
      });

      grouped[order.id].emailResults.push({
        client: order.name,
        email: order.client_email, // Now matches the interface
        success: order.email_success === 1,
        messageId: order.email_message_id || undefined,
      });
    });

    return Object.values(grouped);
  };

  const loadOrderHistory = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.getOrderHistory();

      if (response.success) {
        const groupedOrders = groupOrdersById(response.orders);
        setOrders(groupedOrders);
        setError("");
      } else {
        setError("Erreur lors du chargement de l'historique");
      }
    } catch (error: any) {
      console.error("History error:", error);
      setError("Erreur de connexion au serveur");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteOrder = async (orderId: string) => {
    if (!confirm("Êtes-vous sûr de vouloir supprimer cette commande ?")) {
      return;
    }

    try {
      const response = await apiService.deleteOrder(orderId);

      if (response.success) {
        setOrders(orders.filter((order) => order.id !== orderId));
        if (selectedOrder?.id === orderId) {
          setSelectedOrder(null);
        }
      } else {
        setError("Erreur lors de la suppression");
      }
    } catch (error: any) {
      console.error("Delete error:", error);
      setError("Erreur lors de la suppression");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-100 to-purple-100 p-6 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          </div>
          <span className="text-gray-600 font-medium">
            Chargement de l'historique...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-8">
        <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-3 rounded-xl shadow-lg">
          <History className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            Historique des Commandes
          </h2>
          <p className="text-sm text-gray-500 font-medium">
            Consultez l'historique de toutes vos commandes
          </p>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl shadow-lg">
          <p className="text-red-700 text-sm font-medium">{error}</p>
        </div>
      )}

      {orders.length === 0 ? (
        <div className="text-center py-16 text-gray-500">
          <div className="bg-gradient-to-r from-purple-100 to-indigo-100 p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
            <History className="h-12 w-12 text-purple-500" />
          </div>
          <p className="text-xl font-semibold mb-2 text-gray-700">
            Aucune commande dans l'historique
          </p>
          <p className="text-sm text-gray-500">
            Les commandes envoyées apparaîtront ici
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Orders List */}
          <div className="space-y-4">
            {orders.map((order) => {
              const totalClients = order.clients.length;
              const totalUsd = order.clients.reduce(
                (sum, client) => sum + client.totalUsd,
                0
              );
              const totalMur = order.clients.reduce(
                (sum, client) => sum + client.totalMur,
                0
              );
              const successfulEmails = order.emailResults.filter(
                (r) => r.success
              ).length;

              return (
                <div
                  key={order.id}
                  className={`bg-white rounded-lg shadow-sm border p-4 cursor-pointer transition-colors ${
                    selectedOrder?.id === order.id
                      ? "border-purple-300 bg-purple-50"
                      : "hover:border-gray-300"
                  }`}
                  onClick={() => setSelectedOrder(order)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {formatDate(order.timestamp)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4 text-blue-500" />
                          <span className="text-gray-400">
                            {totalClients} clients
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-4 w-4 text-green-500" />
                          <span className="text-gray-400">
                            ${totalUsd.toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedOrder(order);
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Voir les détails"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteOrder(order.id);
                        }}
                        className="p-1 text-red-400 hover:text-red-600"
                        title="Supprimer"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="text-gray-500">Total MUR:</span>
                      <span className="ml-1 font-medium text-gray-400">
                        Rs {totalMur.toLocaleString()}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Emails:</span>
                      <span className="ml-1 font-medium text-gray-400">
                        {successfulEmails}/{order.emailResults.length}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Order Details */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            {selectedOrder ? (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Détails de la Commande
                </h3>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Date:</span>
                      <p className="font-medium text-gray-500">
                        {formatDate(selectedOrder.timestamp)}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500">ID:</span>
                      <p className="font-medium font-mono text-xs text-gray-500">
                        {selectedOrder.id}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Clients</h4>
                    <div className="space-y-2">
                      {selectedOrder.clients.map((client, index) => {
                        const emailResult = selectedOrder.emailResults[index];
                        return (
                          <div
                            key={index}
                            className="bg-gray-50 p-3 rounded-lg"
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{client.name}</p>
                                <p className="text-sm text-gray-600">
                                  {client.email}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {client.productCount} produits
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-medium">
                                  ${client.totalUsd.toFixed(2)}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Rs {client.totalMur.toLocaleString()}
                                </p>
                                <div className="flex items-center justify-end mt-1">
                                  {emailResult.success ? (
                                    <Check className="h-4 w-4 text-green-500" />
                                  ) : (
                                    <X className="h-4 w-4 text-red-500" />
                                  )}
                                  <span
                                    className={`ml-1 text-xs ${
                                      emailResult.success
                                        ? "text-green-600"
                                        : "text-red-600"
                                    }`}
                                  >
                                    {emailResult.success ? "Envoyé" : "Échec"}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      Résumé Email
                    </h4>
                    <div className="grid grid-cols-3 gap-4 text-center bg-gray-50 p-4 rounded-lg">
                      <div>
                        <p className="text-2xl font-bold text-gray-900">
                          {selectedOrder.emailResults.length}
                        </p>
                        <p className="text-xs text-gray-500">Total</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-green-600">
                          {
                            selectedOrder.emailResults.filter((r) => r.success)
                              .length
                          }
                        </p>
                        <p className="text-xs text-gray-500">Envoyés</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-red-600">
                          {
                            selectedOrder.emailResults.filter((r) => !r.success)
                              .length
                          }
                        </p>
                        <p className="text-xs text-gray-500">Échecs</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <Eye className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Sélectionnez une commande pour voir les détails</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderHistory;
