import { useState, useEffect } from "react";
import { ShoppingCart, History, LogOut } from "lucide-react";
import ClientForm from "./components/ClientForm";
import ResultsDisplay from "./components/ResultsDisplay";
import EmailSection from "./components/EmailSection";
import OrderHistory from "./components/OrderHistory";
import SecurityPage from "./components/SecurityPage";
import { Client, ProcessedClient } from "./types";
import { isAuthenticated, clearAuth } from "./utils/auth";

function App() {
  const [activeTab, setActiveTab] = useState<"form" | "history">("form");
  const [clients, setClients] = useState<Client[]>([]);
  const [results, setResults] = useState<ProcessedClient[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [authenticated, setAuthenticated] = useState<boolean>(false);

  // Check authentication status on component mount
  useEffect(() => {
    setAuthenticated(isAuthenticated());
  }, []);

  const handleAuthenticated = () => {
    setAuthenticated(true);
  };

  const handleLogout = () => {
    clearAuth();
    setAuthenticated(false);
  };

  const tabs = [
    { id: "form", label: "Nouvelle Commande", icon: ShoppingCart },
    { id: "history", label: "Historique", icon: History },
  ];

  // Show security page if not authenticated
  if (!authenticated) {
    return <SecurityPage onAuthenticated={handleAuthenticated} />;
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20 w-full">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl shadow-lg">
                <ShoppingCart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                  Assistant Commandes Shein
                </h1>
                <p className="text-sm text-gray-500 font-medium">
                  Gérez facilement les commandes de vos clients
                </p>
              </div>
            </div>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="flex items-center space-x-2 px-4 py-2.5 text-gray-600 hover:text-gray-800 hover:bg-white/60 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md"
              title="Se déconnecter"
            >
              <LogOut className="h-5 w-5" />
              <span className="hidden sm:inline font-medium">Déconnexion</span>
            </button>
          </div>
        </div>
      </header>
      {/* Navigation Tabs */}
      <nav className="bg-white/70 backdrop-blur-md border-b border-white/30 w-full mt-[90px] shadow-sm">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as "form" | "history")}
                  className={`flex items-center space-x-2 py-4 px-6 rounded-t-xl font-medium text-sm transition-all duration-200 ${
                    activeTab === tab.id
                      ? "bg-white text-blue-600 shadow-lg border-b-2 border-blue-500"
                      : "text-gray-600 hover:text-gray-800 hover:bg-white/50"
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </nav>
      {/* Main Content */}
      <main className="w-full px-4 sm:px-6 lg:px-8 py-8 pt-[112px]">
        <div className="max-w-7xl mx-auto">
          {activeTab === "form" ? (
            <div className="space-y-8">
              {/* Client Form */}
              <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
                <ClientForm
                  clients={clients}
                  setClients={setClients}
                  onResultsUpdate={setResults}
                  isLoading={isLoading}
                  setIsLoading={setIsLoading}
                />
              </div>

              {/* Results Display */}
              {results.length > 0 && (
                <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
                  <ResultsDisplay results={results} setResults={setResults} />
                </div>
              )}

              {/* Email Section */}
              {results.some((r) => r.success) && (
                <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
                  <EmailSection results={results} />
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
              <OrderHistory />
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

export default App;
