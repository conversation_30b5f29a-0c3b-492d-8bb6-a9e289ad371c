// Simple tests for authentication utilities
// Note: These are basic tests. For a full test suite, you'd want to use Jest or Vitest

import {
  authenticate,
  isAuthenticated,
  clearAuth,
  getSecurityCode,
} from "../auth";

// Mock localStorage for testing
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

// Replace the global localStorage with our mock
Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Test functions
export const runAuthTests = async () => {
  console.log("Running authentication tests...");

  // Test 1: Initial state should be unauthenticated
  console.assert(!isAuthenticated(), "Initial state should be unauthenticated");

  // Test 2: Correct code should authenticate (async)
  try {
    const result = await authenticate("1234");
    console.assert(result, "Correct code should authenticate");
    console.assert(
      isAuthenticated(),
      "Should be authenticated after correct code"
    );
  } catch (error) {
    console.log("Authentication test skipped - server not available");
  }

  // Test 3: Incorrect code should not authenticate
  clearAuth();
  try {
    const result = await authenticate("0000");
    console.assert(!result, "Incorrect code should not authenticate");
    console.assert(
      !isAuthenticated(),
      "Should not be authenticated after incorrect code"
    );
  } catch (error) {
    console.log("Authentication test skipped - server not available");
  }

  // Test 4: Clear auth should work
  try {
    await authenticate("1234");
  } catch (error) {
    // Skip if server not available
  }
  clearAuth();
  console.assert(
    !isAuthenticated(),
    "Should not be authenticated after clearAuth"
  );

  // Test 5: Security code should be retrievable
  console.assert(
    getSecurityCode() === "****",
    "Security code should be masked"
  );

  // Test 6: Session expiration (simulate old timestamp)
  const oldTimestamp = Date.now() - 25 * 60 * 60 * 1000; // 25 hours ago
  localStorage.setItem(
    "cart_assistant_auth",
    JSON.stringify({
      isAuthenticated: true,
      timestamp: oldTimestamp,
    })
  );
  console.assert(
    !isAuthenticated(),
    "Expired session should not be authenticated"
  );

  console.log("All authentication tests passed! ✅");
};

// Export for potential use in development
if (typeof window !== "undefined") {
  (window as any).runAuthTests = runAuthTests;
}
