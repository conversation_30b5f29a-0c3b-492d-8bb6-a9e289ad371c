// Simple authentication utilities for session management
import axios from "axios";

const AUTH_KEY = "cart_assistant_auth";
export interface AuthState {
  isAuthenticated: boolean;
  timestamp: number;
}

/**
 * Check if the user is currently authenticated
 */
export const isAuthenticated = (): boolean => {
  try {
    const authData = localStorage.getItem(AUTH_KEY);
    if (!authData) return false;

    const { isAuthenticated: authenticated, timestamp }: AuthState =
      JSON.parse(authData);

    // Check if session is still valid (24 hours)
    const now = Date.now();
    const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    if (now - timestamp > sessionDuration) {
      // Session expired, clear it
      clearAuth();
      return false;
    }

    return authenticated;
  } catch (error) {
    console.error("Error checking authentication:", error);
    return false;
  }
};

/**
 * Authenticate user with the provided code (async version using backend)
 */
export const authenticate = async (code: string): Promise<boolean> => {
  try {
    const response = await axios.post(
      `${
        import.meta.env.VITE_API_BASE_URL || "http://localhost:3001"
      }/api/auth/validate`,
      { code }
    );

    if (response.data.success) {
      // Save authentication state
      const authState: AuthState = {
        isAuthenticated: true,
        timestamp: Date.now(),
      };
      localStorage.setItem(AUTH_KEY, JSON.stringify(authState));
      return true;
    }

    return false;
  } catch (error) {
    console.error("Authentication error:", error);
    return false;
  }
};

/**
 * Authenticate user with the provided code (synchronous fallback)
 * @deprecated Use the async authenticate function instead
 * NOTE: This fallback should be removed in production
 */
export const authenticateSync = (_code: string): boolean => {
  // This is a development fallback only - real validation is server-side
  // In production, remove this function entirely
  console.warn(
    "Using fallback authentication - this should not happen in production"
  );
  return false; // Always fail in production
};

/**
 * Clear authentication state (logout)
 */
export const clearAuth = (): void => {
  localStorage.removeItem(AUTH_KEY);
};

/**
 * Get the security code (for development/testing purposes)
 * NOTE: This function should be removed in production
 * The actual code validation happens on the server side
 */
export const getSecurityCode = (): string => {
  // This is only for development hints - the real validation is server-side
  return "****"; // Code is stored securely on the server
};
