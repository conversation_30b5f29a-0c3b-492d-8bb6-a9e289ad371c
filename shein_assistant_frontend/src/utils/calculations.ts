import { Product } from "../types";

/**
 * Calculate total USD from products array
 */
export const calculateTotalUsd = (products: Product[]): number => {
  return products.reduce((total, product) => {
    const unitPrice = parseFloat(product.price.replace(/[^\d.-]/g, "")) || 0;
    const quantity = product.quantity || 1;
    return total + (unitPrice * quantity);
  }, 0);
};

/**
 * Calculate total MUR from products array using exchange rate
 */
export const calculateTotalMur = (products: Product[], exchangeRate: number): number => {
  const totalUsd = calculateTotalUsd(products);
  return Math.round(totalUsd * exchangeRate * 100) / 100;
};

/**
 * Calculate totals for a single client's data
 */
export const calculateClientTotals = (products: Product[], exchangeRate: number) => {
  const totalUsd = calculateTotalUsd(products);
  const totalMur = calculateTotalMur(products, exchangeRate);
  
  return {
    totalUsd: Math.round(totalUsd * 100) / 100, // Round to 2 decimal places
    totalMur
  };
};
