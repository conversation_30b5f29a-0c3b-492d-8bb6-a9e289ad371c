# Deployment Guide

This guide covers deploying the Shein Assistant application to production environments.

## Environment Configuration

The application is configured to work seamlessly across development and production environments:

### Development URLs
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:3001

### Production URLs
- **Frontend**: https://shein-assistant-frontend.vercel.app
- **Backend**: https://shein-assistant-backend.onrender.com

## Backend Deployment (Render)

### Prerequisites
1. Create a [Render](https://render.com) account
2. Connect your GitHub repository

### Deployment Steps
1. **Create a new Web Service** on Render
2. **Connect your repository** and select the backend folder
3. **Configure the service**:
   - **Name**: `shein-assistant-backend`
   - **Environment**: `Node`
   - **Build Command**: `./render-build.sh`
   - **Start Command**: `npm start`
   - **Root Directory**: `shein_assistant_backend`

4. **Set Environment Variables**:
   ```
   NODE_ENV=production
   PORT=3001
   SECURITY_CODE=your_security_code
   API_BASE_URL=https://shein-assistant-backend.onrender.com
   FRONTEND_URL=https://shein-assistant-frontend.vercel.app
   PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright
   PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your_app_password
   ```

5. **Deploy** - Render will automatically build and deploy your backend

### Playwright Configuration

The application uses Playwright for web scraping. Playwright provides:
- Automatic browser installation and management
- Better reliability in production environments
- Built-in support for hosting platforms like Render
- No manual Chrome configuration required

## Frontend Deployment (Vercel)

### Prerequisites
1. Create a [Vercel](https://vercel.com) account
2. Install Vercel CLI: `npm i -g vercel`

### Deployment Steps
1. **Navigate to frontend directory**:
   ```bash
   cd shein_assistant_frontend
   ```

2. **Deploy with Vercel**:
   ```bash
   vercel
   ```

3. **Configure project settings**:
   - **Framework Preset**: Vite
   - **Root Directory**: `shein_assistant_frontend`
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`

4. **Environment Variables** (automatically loaded from `.env.production`):
   ```
   VITE_API_BASE_URL=https://shein-assistant-backend.onrender.com
   ```

## Environment File Management

### Backend
- **Development**: `.env` (local development)
- **Production**: `.env.production` (production deployment)

The backend automatically loads the correct environment file based on `NODE_ENV`.

### Frontend
- **Development**: `.env` (local development)
- **Production**: `.env.production` (production build)

Vite automatically loads the correct environment file based on the build mode.

## CORS Configuration

The backend is configured to accept requests from:
- Development frontend: `http://localhost:3000`
- Production frontend: `https://shein-assistant-frontend.vercel.app`
- Environment-specific URL from `FRONTEND_URL`

## Database

The application uses SQLite with automatic database initialization. The database file (`orders.db`) is created automatically on first run.

## SMTP Configuration

For email functionality, configure SMTP settings in your environment variables:
- Use Gmail SMTP with an app-specific password
- Ensure 2FA is enabled on your Gmail account
- Generate an app password for the application

## Monitoring and Logs

### Render (Backend)
- View logs in the Render dashboard
- Monitor deployment status and health checks

### Vercel (Frontend)
- View deployment logs in the Vercel dashboard
- Monitor function invocations and performance

## Troubleshooting

### Common Issues

1. **CORS Errors**:
   - Verify frontend URL is in the backend's allowed origins
   - Check environment variables are set correctly

2. **Environment Variables Not Loading**:
   - Ensure `.env.production` files exist
   - Verify environment variables are set in deployment platforms

3. **Database Issues**:
   - Database is created automatically on first run
   - Check file permissions in deployment environment

4. **SMTP Errors**:
   - Verify Gmail app password is correct
   - Ensure SMTP settings match your email provider

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to version control
2. **CORS**: Only allow trusted frontend origins
3. **SMTP**: Use app-specific passwords, not account passwords
4. **Security Code**: Use a strong, unique security code for authentication

## Updates and Maintenance

### Backend Updates
1. Push changes to your repository
2. Render will automatically redeploy

### Frontend Updates
1. Push changes to your repository
2. Vercel will automatically redeploy

### Environment Variable Updates
1. Update variables in deployment platform dashboards
2. Restart services if necessary
