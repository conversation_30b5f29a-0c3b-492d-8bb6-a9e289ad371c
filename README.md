# 🛍️ Shein Order Assistant

A modern React frontend application for managing Shein orders for clients. This application provides an intuitive interface for processing cart data, calculating currency conversions, and managing order history.

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/your-repo.git
cd shein-order-assistant
```

### 2. Install Dependencies

Frontend:
```bash
cd shein_assistant_frontend
npm install
```
Backend:
```bash
cd shein_assistant_backend
npm install
```

### 3. Start the Development Server

Frontend:

```bash
cd shein_assistant_frontend
npm run dev
```

Backend:
```bash
cd shein_assistant_backend
npm run dev
```

The application will be available at `http://localhost:3000`.

## 📖 User Guide

### 1. Getting Started

1. **Access the Application**: Navigate to the frontend URL
2. **Enter Security Code**: Input your 4-character access code
3. **Main Dashboard**: Access the order management interface

### 2. Managing Clients

- **Add Client**: Click "Ajouter Client" to add new clients
- **Client Information**: Enter name, email, and exchange rate
- **Cart Links**: Add multiple Shein cart URLs per client
- **Remove Client**: Use the minus button to remove clients

### 3. Processing Orders

- **Generate Totals**: Click "Générer les Totaux" to process all carts
- **View Results**: See processed data with USD and MUR amounts
- **Product Details**: Expand to view individual products
- **Error Handling**: Failed processes are clearly marked

### 4. Email Management

- **Select Clients**: Choose which clients to send emails to
- **Send Emails**: Click "Envoyer les Emails" to send summaries
- **Track Status**: Monitor email delivery in real-time
- **View Results**: See successful and failed email attempts

### 5. Order History

- **View Orders**: Switch to "Historique" tab
- **Order Details**: Click on orders to see full details
- **Client Information**: View all client data for each order
- **Email Status**: See which emails were sent successfully
- **Delete Orders**: Remove old orders when needed

