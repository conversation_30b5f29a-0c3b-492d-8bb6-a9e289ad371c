// Environment loader script
// This script loads the appropriate .env file based on NODE_ENV

const dotenv = require("dotenv");
const path = require("path");
const fs = require("fs");

// Determine which environment file to load
const nodeEnv = process.env.NODE_ENV || "development";
const envFile = nodeEnv === "production" ? ".env.production" : ".env";
const envPath = path.resolve(__dirname, envFile);

console.log(`Loading environment from: ${envFile}`);
console.log(`Environment file path: ${envPath}`);
console.log(`File exists: ${fs.existsSync(envPath)}`);

// Load the environment file
const result = dotenv.config({ path: envPath });

if (result.error) {
  console.warn(`Warning: Could not load ${envFile}, falling back to .env`);
  console.warn(`Error details:`, result.error.message);

  // Try to set production environment variables manually if in production
  if (nodeEnv === "production") {
    console.log("Setting production environment variables manually...");
    process.env.PORT = process.env.PORT || "3001";
    process.env.API_BASE_URL = "https://shein-assistant-backend.onrender.com";
    process.env.FRONTEND_URL = "https://shein-assistant-frontend.vercel.app";
    process.env.NODE_ENV = "production";
    console.log("✅ Production environment variables set manually");
  }

  // Fallback to default .env if specific env file doesn't exist
  dotenv.config();
} else {
  console.log(`✅ Environment loaded successfully from ${envFile}`);
}

module.exports = result;
