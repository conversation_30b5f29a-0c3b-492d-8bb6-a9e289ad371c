#!/usr/bin/env node

// Test script for the Shein scraping functionality
const axios = require("axios");

const BASE_URL = "http://localhost:3001";

// Test data with the provided Shein link
const testData = {
  clients: [
    {
      id: "test-client-1",
      name: "Test Client",
      email: "marie<PERSON><PERSON><PERSON><EMAIL>",
      cartLinks: [
        "http://api-shein.shein.com/h5/sharejump/appjump?link=l2jKrO0cFPd_b&localcountry=FR&url_from=GM73978189359"
      ]
    }
  ],
  exchange_rate: 45.5,
  orderDate: new Date().toISOString(),
  currency: {
    from: "USD",
    to: "MUR"
  }
};

async function testScraping() {
  console.log("🧪 Testing Shein Cart Scraping...");
  console.log("📋 Test Data:");
  console.log(`  Client: ${testData.clients[0].name}`);
  console.log(`  Email: ${testData.clients[0].email}`);
  console.log(`  Cart Link: ${testData.clients[0].cartLinks[0]}`);
  console.log(`  Exchange Rate: ${testData.exchange_rate} (USD to MUR)`);
  console.log("");

  try {
    console.log("🚀 Sending request to /api/generate...");
    
    const response = await axios.post(`${BASE_URL}/api/generate`, testData, {
      timeout: 120000, // 2 minutes timeout
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log("✅ Response received!");
    console.log("📊 Results:");
    
    if (response.data.success) {
      const result = response.data.results[0];
      
      console.log(`  Client: ${result.client}`);
      console.log(`  Success: ${result.success}`);
      
      if (result.success && result.data) {
        console.log(`  Products Found: ${result.data.productCount}`);
        console.log(`  Total USD: $${result.data.totalUsd}`);
        console.log(`  Total MUR: Rs ${result.data.totalMur}`);
        console.log(`  Cart Count: ${result.data.cartCount}`);
        console.log(`  Exchange Rate: ${result.data.exchangeRate}`);
        
        if (result.data.products && result.data.products.length > 0) {
          console.log("\n🛍️ Products:");
          result.data.products.forEach((product, index) => {
            console.log(`    ${index + 1}. ${product.name}`);
            console.log(`       Price: ${product.price} (Qty: ${product.quantity})`);
            console.log(`       Raw Price: ${product.rawPrice}`);
          });
        } else {
          console.log("  ⚠️ No products found in the cart");
        }
      } else if (result.error) {
        console.log(`  ❌ Error: ${result.error}`);
      }
    } else {
      console.log(`  ❌ Request failed: ${response.data.message}`);
    }

  } catch (error) {
    console.error("❌ Test failed:");
    if (error.response) {
      console.error(`  Status: ${error.response.status}`);
      console.error(`  Message: ${error.response.data?.message || error.response.statusText}`);
      if (error.response.data?.error) {
        console.error(`  Error: ${error.response.data.error}`);
      }
    } else if (error.request) {
      console.error("  No response received from server");
      console.error("  Make sure the backend server is running on http://localhost:3001");
    } else {
      console.error(`  Error: ${error.message}`);
    }
  }
}

// Check if server is running first
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/health`);
    console.log("✅ Backend server is running");
    return true;
  } catch (error) {
    console.log("❌ Backend server is not running. Please start it first:");
    console.log("   cd shein_assistant_backend && npm run dev");
    return false;
  }
}

// Run the test
(async () => {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testScraping();
  }
})();
