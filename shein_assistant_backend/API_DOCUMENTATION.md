# Shein Assistant Backend API Documentation

## Overview
This is the backend API for the Shein Assistant application. It provides endpoints for authentication, order generation, email sending, and order history management.

## Base URLs

### Development
```
http://localhost:3001
```

### Production
```
https://shein-assistant-backend.onrender.com
```

## Environment Configuration

The application supports both development and production environments with automatic environment file loading:

- **Development**: Uses `.env` file
- **Production**: Uses `.env.production` file

### Environment Variables
- `NODE_ENV`: Environment mode (development/production)
- `PORT`: Server port (default: 3001)
- `SECURITY_CODE`: Authentication security code
- `API_BASE_URL`: API base URL
- `FRONTEND_URL`: Frontend URL for CORS
- `SMTP_HOST`: SMTP server host
- `SMTP_PORT`: SMTP server port
- `SMTP_USER`: SMTP username
- `SMTP_PASS`: SMTP password

### CORS Configuration
The server automatically allows requests from:
- `http://localhost:3000` (Development frontend)
- `https://shein-assistant-frontend.vercel.app` (Production frontend)
- Environment-specific URL from `FRONTEND_URL`

## API Endpoints

### Health Check

#### GET /health
Basic health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "message": "Server is running",
  "timestamp": "2025-07-05T10:30:00.000Z"
}
```

#### GET /api/health
API-specific health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "message": "API is running",
  "timestamp": "2025-07-05T10:30:00.000Z"
}
```

### Authentication

#### POST /api/auth/validate
Validates the security code for authentication.

**Request Body:**
```json
{
  "code": "7330"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Authentication successful"
}
```

**Response (Failure):**
```json
{
  "success": false,
  "message": "Invalid code"
}
```

### Order Generation

#### POST /api/generate
Generates order summaries by processing client cart data.

**Request Body:**
```json
{
  "clients": [
    {
      "id": "client-1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "cartLinks": ["https://shein.com/cart1", "https://shein.com/cart2"]
    }
  ],
  "exchange_rate": 45.5,
  "orderDate": "2025-07-05T10:30:00.000Z",
  "currency": {
    "from": "USD",
    "to": "MUR"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Orders generated successfully",
  "results": [
    {
      "client": "John Doe",
      "email": "<EMAIL>",
      "success": true,
      "data": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "products": [],
        "totalUsd": 0,
        "totalMur": 0,
        "cartCount": 2,
        "exchangeRate": 45.5,
        "productCount": 0
      }
    }
  ]
}
```

### Email Sending

#### POST /api/send
Sends emails to selected clients with their order summaries.

**Request Body:**
```json
{
  "selectedClients": [
    {
      "name": "John Doe",
      "email": "<EMAIL>",
      "products": [],
      "totalUsd": 100,
      "totalMur": 4550,
      "cartCount": 1,
      "exchangeRate": 45.5,
      "productCount": 5
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Emails sent successfully",
  "results": [
    {
      "client": "John Doe",
      "email": "<EMAIL>",
      "success": true,
      "messageId": "msg_1720174200000_abc123def"
    }
  ],
  "summary": {
    "total": 1,
    "successful": 1,
    "failed": 0
  }
}
```

### Order History

#### GET /api/history
Retrieves all order history records.

**Response:**
```json
{
  "success": true,
  "orders": []
}
```

#### GET /api/history/:id
Retrieves a specific order by ID.

**Response:**
```json
{
  "success": true,
  "order": {
    "id": "order-123",
    "timestamp": "2025-07-05T10:30:00.000Z",
    "name": "Mock Order",
    "client_email": "<EMAIL>",
    "total_usd": 0,
    "total_mur": 0,
    "exchange_rate": 1,
    "product_count": 0,
    "email_success": 1
  }
}
```

#### DELETE /api/history/:id
Deletes a specific order by ID.

**Response:**
```json
{
  "success": true,
  "message": "Order order-123 deleted successfully"
}
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

**400 Bad Request:**
```json
{
  "success": false,
  "message": "Validation error message"
}
```

**404 Not Found:**
```json
{
  "success": false,
  "message": "API endpoint /api/nonexistent not found"
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "error": "Error message"
}
```

## Testing

Run the API test suite:
```bash
node test-apis.js
```

This will test all endpoints and verify they're working correctly.

## Development

Start the development server:
```bash
npm run dev
```

Build for production:
```bash
npm run build
npm start
```

## Notes

- Most endpoints currently use mock implementations
- Replace mock logic with actual business logic as needed
- The authentication system is basic and should be enhanced for production
- Add proper database integration for order history
- Implement actual email sending functionality
- Add proper cart scraping logic for order generation
