#!/bin/bash

# Render build script for Shein Assistant Backend
echo "🚀 Starting Render build process..."

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Set Playwright environment variables for Render
export PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false

# Create Playwright cache directory
echo "📁 Creating Playwright cache directory..."
mkdir -p /opt/render/.cache/ms-playwright

# Install Playwright browsers with explicit path
echo "🌐 Installing Playwright browsers..."
echo "Cache directory: $PLAYWRIGHT_BROWSERS_PATH"

# Force install all Playwright browsers
echo "🔄 Installing Chromium browser..."
if PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright npx playwright install chromium --with-deps; then
    echo "✅ Playwright Chromium installed successfully!"
else
    echo "❌ Chromium installation failed, trying without deps..."
    PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright npx playwright install chromium || echo "Chromium installation failed"
fi

# Verify installation
echo "📋 Verifying browser installation..."
ls -la /opt/render/.cache/ms-playwright/ || echo "Cache directory not found"

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

echo "✅ Build completed successfully!"