// Load environment variables first
require("../load-env");

import express from "express";
import cors from "cors";
import { authenticate } from "./services/auth";
import { SheinScraper } from "./services/sheinScraper";
import { emailService } from "./services/emailService";
import { databaseService } from "./services/database";
import { Client, ProcessedClient, EmailResult, OrderHistory } from "./types";

const app = express();
const port = process.env.PORT || 3001;

// CORS configuration - Allow both development and production frontend URLs
const allowedOrigins = [
  "http://localhost:3000", // Development frontend (default)
  "http://localhost:3001", // Development frontend (alternative port)
  "https://shein-assistant-frontend.vercel.app", // Production frontend
  process.env.FRONTEND_URL, // Environment-specific URL
].filter(Boolean); // Remove any undefined values

app.use(
  cors({
    origin: (origin, callback) => {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);

      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      } else {
        return callback(new Error("Not allowed by CORS"));
      }
    },
    credentials: true,
  })
);

app.use(express.json());

// Health check endpoint
app.get("/health", (_, res) => {
  res.json({
    status: "healthy",
    message: "Server is running",
    timestamp: new Date().toISOString(),
  });
});

// API Health check endpoint (for consistency with api.ts)
app.get("/api/health", (_, res) => {
  res.json({
    status: "healthy",
    message: "API is running",
    timestamp: new Date().toISOString(),
  });
});

// Authentication endpoint
app.post("/api/auth/validate", async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: "Security code is required",
      });
    }

    const isValid = await authenticate(code);
    res.json({
      success: isValid,
      message: isValid ? "Authentication successful" : "Invalid code",
    });
  } catch (error) {
    console.error("Auth error:", error);
    res.status(500).json({
      success: false,
      message: "Authentication service error",
    });
  }
});

// Generate orders endpoint
app.post("/api/generate", async (req, res) => {
  try {
    const { clients, exchange_rate, orderDate, currency } = req.body;

    // Validate required fields
    if (!clients || !Array.isArray(clients)) {
      return res.status(400).json({
        success: false,
        message: "Clients array is required",
      });
    }

    if (!exchange_rate || typeof exchange_rate !== "number") {
      return res.status(400).json({
        success: false,
        message: "Valid exchange rate is required",
      });
    }

    // Process each client's cart links using the scraping service
    const results: ProcessedClient[] = [];

    for (const client of clients) {
      try {
        console.log(
          `Processing client: ${client.name} with ${client.cartLinks.length} cart links`
        );

        // Scrape all cart links for this client
        const scrapingResult = await SheinScraper.scrapeMultipleCarts(
          client.cartLinks
        );

        if (scrapingResult.success) {
          results.push({
            client: client.name,
            email: client.email,
            success: true,
            data: {
              name: client.name,
              email: client.email,
              products: scrapingResult.products,
              totalUsd: scrapingResult.totalUsd,
              totalMur:
                Math.round(scrapingResult.totalUsd * exchange_rate * 100) / 100,
              cartCount: client.cartLinks.length,
              exchangeRate: exchange_rate,
              productCount: scrapingResult.products.length,
            },
          });
        } else {
          results.push({
            client: client.name,
            email: client.email,
            success: false,
            error: scrapingResult.error || "Failed to scrape cart data",
          });
        }
      } catch (error: any) {
        console.error(`Error processing client ${client.name}:`, error);
        results.push({
          client: client.name,
          email: client.email,
          success: false,
          error: `Processing error: ${error.message}`,
        });
      }
    }

    res.json({
      success: true,
      message: "Orders generated successfully",
      results,
    });
  } catch (error) {
    console.error("Generate orders error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// Send emails endpoint
app.post("/api/send", async (req, res) => {
  try {
    const { selectedClients } = req.body;

    if (!selectedClients || !Array.isArray(selectedClients)) {
      return res.status(400).json({
        success: false,
        message: "Selected clients array is required",
      });
    }

    console.log(`📧 Sending emails to ${selectedClients.length} clients...`);

    // Vérifier la configuration SMTP
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      return res.status(500).json({
        success: false,
        error:
          "Configuration SMTP manquante. Veuillez configurer SMTP_USER et SMTP_PASS dans le fichier .env",
      });
    }

    // Tester la connexion SMTP
    const connectionTest = await emailService.testConnection();
    if (!connectionTest) {
      return res.status(500).json({
        success: false,
        error:
          "Impossible de se connecter au serveur SMTP. Vérifiez vos paramètres de configuration.",
      });
    }

    // Envoyer les emails
    const { results, summary } = await emailService.sendEmails(selectedClients);

    console.log(
      `📊 Résultats envoi: ${summary.successful}/${summary.total} réussis`
    );

    // Sauvegarder la commande dans la base de données
    try {
      const orderId = `order-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      // Convertir selectedClients en format ProcessedClient pour la sauvegarde
      const processedClients: ProcessedClient[] = selectedClients.map(
        (client) => ({
          client: client.name,
          email: client.email,
          success: true,
          data: client,
        })
      );

      await databaseService.saveOrder(orderId, processedClients, results);
      console.log(`💾 Order ${orderId} saved to database`);
    } catch (dbError) {
      console.error("❌ Failed to save order to database:", dbError);
      // Continue with response even if database save fails
    }

    res.json({
      success: summary.successful > 0,
      message:
        summary.failed === 0
          ? "Tous les emails ont été envoyés avec succès"
          : `${summary.successful} emails envoyés, ${summary.failed} échecs`,
      results,
      summary,
    });
  } catch (error) {
    console.error("Send emails error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: `Erreur lors de l'envoi des emails: ${error.message}`,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "Une erreur inconnue s'est produite lors de l'envoi des emails",
      });
    }
  }
});

// Get order history endpoint
app.get("/api/history", async (req, res) => {
  try {
    const orders = await databaseService.getOrderHistory();

    res.json({
      success: true,
      orders,
    });
  } catch (error) {
    console.error("Get history error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// Get specific order endpoint
app.get("/api/history/:id", async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required",
      });
    }

    const order = await databaseService.getOrder(id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    res.json({
      success: true,
      order,
    });
  } catch (error) {
    console.error("Get order error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// Delete order endpoint
app.delete("/api/history/:id", async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required",
      });
    }

    const deleted = await databaseService.deleteOrder(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    res.json({
      success: true,
      message: `Order ${id} deleted successfully`,
    });
  } catch (error) {
    console.error("Delete order error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// 404 handler for unknown API routes
app.use("/api/*", (req, res) => {
  res.status(404).json({
    success: false,
    message: `API endpoint ${req.path} not found`,
  });
});

// Global error handler
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    console.error("Global error:", err);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
);

// Initialize database and start server
async function startServer() {
  try {
    // Initialize database
    await databaseService.initialize();

    app.listen(port, () => {
      console.log(`Server running on http://localhost:${port}`);
      console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
      console.log(`Allowed CORS origins: ${allowedOrigins.join(", ")}`);
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Shutting down server...");
  await databaseService.close();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 Shutting down server...");
  await databaseService.close();
  process.exit(0);
});

startServer();
