import nodemailer from "nodemailer";
import { ProcessedClient, EmailResult } from "../types";

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    // Configuration SMTP - utilisez vos propres paramètres
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || "smtp.gmail.com",
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: false, // true pour 465, false pour autres ports
      auth: {
        user: process.env.SMTP_USER, // votre email
        pass: process.env.SMTP_PASS, // votre mot de passe d'application
      },
      tls: {
        rejectUnauthorized: false,
      },
    });
  }

  /**
   * Calculate dynamic totals from current product quantities
   */
  private calculateDynamicTotals(clientData: ProcessedClient["data"]) {
    if (!clientData?.products) {
      return { totalUsd: 0, totalMur: 0, productCount: 0 };
    }

    let totalUsd = 0;
    let productCount = 0;

    clientData.products.forEach((product) => {
      // Parse price robustly
      let price = 0;
      if (typeof product.price === "string") {
        const priceMatch = product.price.match(/[\d.,]+/);
        price = priceMatch ? parseFloat(priceMatch[0].replace(",", ".")) : 0;
      } else if (typeof product.price === "number") {
        price = product.price;
      }

      totalUsd += price * product.quantity;
      productCount += product.quantity;
    });

    const totalMur = Math.round(totalUsd * clientData.exchangeRate * 100) / 100;

    return {
      totalUsd: Math.round(totalUsd * 100) / 100,
      totalMur,
      productCount,
    };
  }

  /**
   * Génère le contenu HTML de l'email
   */
  private generateEmailHTML(clientData: ProcessedClient["data"]): string {
    if (!clientData) return "";

    // Calculate dynamic totals based on current quantities
    const dynamicTotals = this.calculateDynamicTotals(clientData);

    const productsHTML = clientData.products
      .map((product) => {
        console.log(`📧 Email processing product:`, {
          name: product.name,
          price: product.price,
          rawPrice: product.rawPrice,
          quantity: product.quantity,
        });

        // Parse price more robustly
        let price = 0;
        if (typeof product.price === "string") {
          // Remove any currency symbols and extract number
          const priceMatch = product.price.match(/[\d.,]+/);
          price = priceMatch ? parseFloat(priceMatch[0].replace(",", ".")) : 0;
        } else if (typeof product.price === "number") {
          price = product.price;
        }

        console.log(`💰 Parsed price: ${price}`);

        const total = price * product.quantity;
        return `
          <tr style="border-bottom: 1px solid #eee;">
            <td style="padding: 12px; text-align: left;">${product.name}</td>
            <td style="padding: 12px; text-align: center;">${
              product.quantity
            }</td>
            <td style="padding: 12px; text-align: right;">$${price.toFixed(
              2
            )}</td>
            <td style="padding: 12px; text-align: right;">$${total.toFixed(
              2
            )}</td>
          </tr>
        `;
      })
      .join("");

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Résumé de votre commande SHEIN</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }
          .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .table th { background: #007bff; color: white; padding: 12px; text-align: left; }
          .table td { padding: 12px; border-bottom: 1px solid #eee; }
          .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px; }
          .total { font-size: 18px; font-weight: bold; color: #007bff; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🛍️ Résumé de votre commande SHEIN</h1>
            <p>Bonjour ${clientData.name},</p>
            <p>Voici le résumé de votre commande SHEIN</p>
          </div>

          <table class="table">
            <thead>
              <tr>
                <th>Produit</th>
                <th style="text-align: center;">Quantité</th>
                <th style="text-align: right;">Prix unitaire</th>
                <th style="text-align: right;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${productsHTML}
            </tbody>
          </table>

          <div class="summary">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span>Nombre de produits:</span>
              <span>${dynamicTotals.productCount}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span>Total USD:</span>
              <span>$${dynamicTotals.totalUsd.toFixed(2)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span>Taux de change:</span>
              <span>1 USD = ${clientData.exchangeRate} MUR</span>
            </div>
            <hr style="margin: 15px 0;">
            <div style="display: flex; justify-content: space-between;" class="total">
              <span>Total MUR:</span>
              <span>Rs ${dynamicTotals.totalMur.toFixed(2)}</span>
            </div>
          </div>

          <div class="footer">
            <p>Merci pour votre confiance !</p>
            <p><small>Cet email a été généré automatiquement par le système de commandes SHEIN.</small></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Envoie un email à un client
   */
  async sendEmail(clientData: ProcessedClient["data"]): Promise<EmailResult> {
    if (!clientData) {
      return {
        client: "Unknown",
        email: "<EMAIL>",
        success: false,
        error: "Données client manquantes",
      };
    }

    // Calculate dynamic totals for email
    const dynamicTotals = this.calculateDynamicTotals(clientData);

    try {
      const mailOptions = {
        from: `"Assistant SHEIN" <${process.env.SMTP_USER}>`,
        to: clientData.email,
        subject: `🛍️ Résumé de votre commande SHEIN - Rs ${dynamicTotals.totalMur.toFixed(
          2
        )}`,
        html: this.generateEmailHTML(clientData),
        text: `
Bonjour ${clientData.name},

Voici le résumé de votre commande SHEIN:

Nombre de produits: ${dynamicTotals.productCount}
Total USD: $${dynamicTotals.totalUsd.toFixed(2)}
Taux de change: 1 USD = ${clientData.exchangeRate} MUR
Total MUR: Rs ${dynamicTotals.totalMur.toFixed(2)}

Merci pour votre confiance !
        `.trim(),
      };

      const info = await this.transporter.sendMail(mailOptions);

      return {
        client: clientData.name,
        email: clientData.email,
        success: true,
        messageId: info.messageId,
      };
    } catch (error: any) {
      console.error(`Erreur envoi email pour ${clientData.name}:`, error);
      return {
        client: clientData.name,
        email: clientData.email,
        success: false,
        error: error.message || "Erreur inconnue lors de l'envoi",
      };
    }
  }

  /**
   * Envoie des emails à plusieurs clients
   */
  async sendEmails(clientsData: ProcessedClient["data"][]): Promise<{
    results: EmailResult[];
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }> {
    const results: EmailResult[] = [];

    // Envoyer les emails un par un pour éviter les limites de taux
    for (const clientData of clientsData) {
      const result = await this.sendEmail(clientData);
      results.push(result);

      // Petite pause entre les envois pour éviter les limites
      if (clientsData.length > 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    const summary = {
      total: results.length,
      successful: results.filter((r) => r.success).length,
      failed: results.filter((r) => !r.success).length,
    };

    return { results, summary };
  }

  /**
   * Teste la connexion SMTP
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log("✅ Connexion SMTP réussie");
      return true;
    } catch (error: any) {
      console.error("❌ Erreur connexion SMTP:", error.message);
      return false;
    }
  }
}

export const emailService = new EmailService();
