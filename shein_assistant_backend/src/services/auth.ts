// Simple server-side authentication
require("dotenv").config();

const SECURITY_CODE = process.env.SECURITY_CODE || "1234"; // In production, use environment variables

export const authenticate = async (code: string): Promise<boolean> => {
  return code === SECURITY_CODE;
};

export const validateAuth = (authHeader?: string): boolean => {
  if (!authHeader) return false;
  const token = authHeader.replace("Bearer ", "");
  return token === SECURITY_CODE;
};
