import sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';
import path from 'path';
import { OrderHistoryRow, ProcessedClient, EmailResult } from '../types';

class DatabaseService {
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

  async initialize(): Promise<void> {
    try {
      // Create database file in the project root
      const dbPath = path.join(process.cwd(), 'orders.db');
      console.log(`📁 Initializing database at: ${dbPath}`);

      this.db = await open({
        filename: dbPath,
        driver: sqlite3.Database
      });

      // Create tables if they don't exist
      await this.createTables();
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Create orders table
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        timestamp TEXT NOT NULL,
        name TEXT NOT NULL,
        client_email TEXT NOT NULL,
        total_usd REAL NOT NULL,
        total_mur REAL NOT NULL,
        exchange_rate REAL NOT NULL,
        product_count INTEGER NOT NULL,
        email_success INTEGER NOT NULL,
        email_message_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create products table for detailed product information
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS order_products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id TEXT NOT NULL,
        client_email TEXT NOT NULL,
        product_name TEXT NOT NULL,
        product_price TEXT NOT NULL,
        product_quantity INTEGER NOT NULL,
        raw_price TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `);

    console.log('✅ Database tables created/verified');
  }

  async saveOrder(
    orderId: string,
    processedClients: ProcessedClient[],
    emailResults: EmailResult[]
  ): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run('BEGIN TRANSACTION');

      const timestamp = new Date().toISOString();

      for (const client of processedClients) {
        if (!client.success || !client.data) continue;

        // Find corresponding email result
        const emailResult = emailResults.find(
          (result) => result.email === client.email
        );

        // Insert order record
        await this.db.run(
          `INSERT INTO orders (
            id, timestamp, name, client_email, total_usd, total_mur,
            exchange_rate, product_count, email_success, email_message_id
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            orderId,
            timestamp,
            client.data.name,
            client.data.email,
            client.data.totalUsd,
            client.data.totalMur,
            client.data.exchangeRate,
            client.data.productCount,
            emailResult?.success ? 1 : 0,
            emailResult?.messageId || null
          ]
        );

        // Insert product records
        for (const product of client.data.products) {
          await this.db.run(
            `INSERT INTO order_products (
              order_id, client_email, product_name, product_price,
              product_quantity, raw_price
            ) VALUES (?, ?, ?, ?, ?, ?)`,
            [
              orderId,
              client.data.email,
              product.name,
              product.price,
              product.quantity,
              product.rawPrice
            ]
          );
        }
      }

      await this.db.run('COMMIT');
      console.log(`✅ Order ${orderId} saved to database`);
    } catch (error) {
      await this.db.run('ROLLBACK');
      console.error('❌ Failed to save order:', error);
      throw error;
    }
  }

  async getOrderHistory(): Promise<OrderHistoryRow[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const orders = await this.db.all<OrderHistoryRow[]>(
        `SELECT 
          id, timestamp, name, client_email, total_usd, total_mur,
          exchange_rate, product_count, email_success, email_message_id
        FROM orders 
        ORDER BY timestamp DESC`
      );

      console.log(`📋 Retrieved ${orders.length} orders from database`);
      return orders;
    } catch (error) {
      console.error('❌ Failed to get order history:', error);
      throw error;
    }
  }

  async getOrder(id: string): Promise<OrderHistoryRow | null> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const order = await this.db.get<OrderHistoryRow>(
        `SELECT 
          id, timestamp, name, client_email, total_usd, total_mur,
          exchange_rate, product_count, email_success, email_message_id
        FROM orders 
        WHERE id = ?`,
        [id]
      );

      return order || null;
    } catch (error) {
      console.error(`❌ Failed to get order ${id}:`, error);
      throw error;
    }
  }

  async deleteOrder(id: string): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run('BEGIN TRANSACTION');

      // Delete products first (foreign key constraint)
      await this.db.run('DELETE FROM order_products WHERE order_id = ?', [id]);

      // Delete order
      const result = await this.db.run('DELETE FROM orders WHERE id = ?', [id]);

      await this.db.run('COMMIT');

      const deleted = (result.changes || 0) > 0;
      if (deleted) {
        console.log(`✅ Order ${id} deleted from database`);
      } else {
        console.log(`⚠️ Order ${id} not found in database`);
      }

      return deleted;
    } catch (error) {
      await this.db.run('ROLLBACK');
      console.error(`❌ Failed to delete order ${id}:`, error);
      throw error;
    }
  }

  async getOrderProducts(orderId: string, clientEmail?: string) {
    if (!this.db) throw new Error('Database not initialized');

    try {
      let query = `
        SELECT product_name, product_price, product_quantity, raw_price
        FROM order_products 
        WHERE order_id = ?
      `;
      const params: any[] = [orderId];

      if (clientEmail) {
        query += ' AND client_email = ?';
        params.push(clientEmail);
      }

      query += ' ORDER BY id';

      const products = await this.db.all(query, params);
      return products;
    } catch (error) {
      console.error(`❌ Failed to get products for order ${orderId}:`, error);
      throw error;
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
      console.log('📁 Database connection closed');
    }
  }
}

// Create singleton instance
export const databaseService = new DatabaseService();
