# Configuration de l'envoi d'emails

## Configuration Gmail

Pour utiliser Gmail comme serveur SMTP, suivez ces étapes :

### 1. Activer l'authentification à deux facteurs
1. Allez dans votre compte Google
2. <PERSON><PERSON><PERSON><PERSON><PERSON> → Authentification à deux facteurs
3. Activez l'authentification à deux facteurs

### 2. Générer un mot de passe d'application
1. Allez dans votre compte Google
2. <PERSON><PERSON><PERSON>rit<PERSON> → Mots de passe d'application
3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> "Autre (nom personnalisé)"
4. <PERSON><PERSON><PERSON> "SHEIN Assistant" 
5. <PERSON><PERSON><PERSON> sur "Générer"
6. Copiez le mot de passe généré (16 caractères)

### 3. Configurer le fichier .env
Modifiez le fichier `.env` dans le dossier `shein_assistant_backend` :

```env
# Configuration SMTP pour l'envoi d'emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=votre-mot-de-passe-application-16-caracteres
```

**Important :** 
- Remplacez `<EMAIL>` par votre vraie adresse Gmail
- Remplacez `votre-mot-de-passe-application-16-caracteres` par le mot de passe d'application généré à l'étape 2

## Configuration pour d'autres fournisseurs

### Outlook/Hotmail
```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=votre-mot-de-passe
```

### Yahoo Mail
```env
SMTP_HOST=smtp.mail.yahoo.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=votre-mot-de-passe-application
```

## Test de la configuration

Une fois configuré, redémarrez le serveur :

```bash
cd shein_assistant_backend
npm run dev
```

Le serveur testera automatiquement la connexion SMTP au démarrage.

## Dépannage

### Erreur "Invalid login"
- Vérifiez que l'authentification à deux facteurs est activée
- Vérifiez que vous utilisez un mot de passe d'application et non votre mot de passe normal
- Vérifiez que l'email et le mot de passe sont corrects dans le fichier .env

### Erreur "Connection timeout"
- Vérifiez votre connexion internet
- Vérifiez que le port 587 n'est pas bloqué par votre firewall
- Essayez le port 465 avec `secure: true`

### Erreur "Authentication failed"
- Vérifiez que l'accès aux applications moins sécurisées est activé (pour les anciens comptes)
- Utilisez un mot de passe d'application au lieu du mot de passe principal

## Sécurité

⚠️ **Important pour la sécurité :**
- Ne partagez jamais votre fichier `.env`
- Ajoutez `.env` à votre `.gitignore`
- Utilisez toujours des mots de passe d'application
- Révoquezles mots de passe d'application si vous n'en avez plus besoin
