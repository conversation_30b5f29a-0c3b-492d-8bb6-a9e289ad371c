#!/bin/bash

# Production startup script for Shein Assistant Backend
echo "🚀 Starting production server..."

# Set Playwright environment variables
export PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false

# Ensure browsers are installed before starting the server
echo "🔍 Checking Playwright browser installation..."

# Only try to create Render-specific directories if we're actually on Render
if [ -w "/opt" ] 2>/dev/null; then
    echo "📁 Creating Render cache directory..."
    mkdir -p /opt/render/.cache/ms-playwright

    # Check if browsers are already installed
    if [ ! -d "/opt/render/.cache/ms-playwright/chromium-"* ]; then
        echo "📦 Installing Playwright browsers..."
        PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright npx playwright install chromium --with-deps || \
        PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright npx playwright install chromium || \
        echo "⚠️ Browser installation failed, will try to use system browser"
    else
        echo "✅ Playwright browsers already installed"
    fi

    # List installed browsers for debugging
    echo "📋 Checking browser installation..."
    ls -la /opt/render/.cache/ms-playwright/ || echo "No browsers found"
else
    echo "🏠 Local environment detected, using default Playwright browser installation"
    npx playwright install chromium || echo "⚠️ Local browser installation failed"
fi

# Start the server
echo "🌟 Starting Node.js server..."
NODE_ENV=production node dist/server.js
